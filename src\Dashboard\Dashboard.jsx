import React from 'react'
import { AiOutlineUser, AiOutlineFileText, AiOutlineRise, AiOutlineFall } from 'react-icons/ai'

export default function Dashboard() {
  const stats = [
    {
      title: "إجمالي عدد الأطفال",
      value: "232",
      change: "-0.5%",
      changeType: "decrease",
      icon: AiOutlineUser,
      color: "blue"
    },
    {
      title: "عدد الأطفال الجدد",
      value: "232",
      change: "+22%",
      changeType: "increase",
      icon: AiOutlineRise,
      color: "green"
    },
    {
      title: "متوسط عدد التحليلات اليومية",
      value: "232",
      change: "-0.5%",
      changeType: "decrease",
      icon: AiOutlineFileText,
      color: "purple"
    },
    {
      title: "عدد الرسومات التي تم رفعها",
      value: "232",
      change: "-0.5%",
      changeType: "decrease",
      icon: AiOutlineFileText,
      color: "orange"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="text-right">
            <h1 className="text-2xl font-bold text-gray-800 mb-2">
              مرحباً بك من جديد [اسم المؤسسة] 👋
            </h1>
            <p className="text-gray-600">
              إليك لمحة سريعة عن حالة الأطفال - كل شيء في مكانه بشكل ⭐
            </p>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg">
            <span className="text-blue-600 font-semibold">GazaKidMindCanvas</span>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-lg ${
                stat.color === 'blue' ? 'bg-blue-50 text-blue-600' :
                stat.color === 'green' ? 'bg-green-50 text-green-600' :
                stat.color === 'purple' ? 'bg-purple-50 text-purple-600' :
                'bg-orange-50 text-orange-600'
              }`}>
                <stat.icon size={24} />
              </div>
            </div>

            <div className="text-right">
              <h3 className="text-sm text-gray-600 mb-2">{stat.title}</h3>
              <div className="flex items-center justify-between">
                <div className={`flex items-center text-sm ${
                  stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.changeType === 'increase' ?
                    <AiOutlineRise className="ml-1" /> :
                    <AiOutlineFall className="ml-1" />
                  }
                  {stat.change}
                </div>
                <span className="text-2xl font-bold text-gray-800">{stat.value}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Summary Section */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h2 className="text-xl font-bold text-gray-800 mb-4 text-right">ملخص الأسبوع</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 bg-red-50 rounded-lg">
            <div className="text-red-600 font-bold text-lg mb-2">100+ طفل</div>
            <div className="text-sm text-gray-600">حالة نفسية سيئة - تتطلب مراقبة فورية من الأخصائيين النفسيين</div>
          </div>

          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-gray-600 font-bold text-lg mb-2">0</div>
            <div className="text-sm text-gray-600">حالة معتدلة - لا توجد إشارات واضحة تحتاج لتدخل فوري</div>
          </div>

          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-green-600 font-bold text-lg mb-2">100+ طفل</div>
            <div className="text-sm text-gray-600">حالة نفسية جيدة - تتمتع بمؤشرات مستقرة أو متحسنة</div>
          </div>
        </div>
      </div>
    </div>
  )
}
