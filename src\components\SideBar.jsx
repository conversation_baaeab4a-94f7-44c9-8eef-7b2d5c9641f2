import React, { useState } from 'react'
import { NavLink } from 'react-router-dom';
import logo from "../assets/logo192.png";
import {
  AiOutlineHome,
  AiOutlineUser,
  AiOutlineBulb,
  AiOutlinePlus,
  AiOutlineCloudUpload,
  AiOutlineSetting,
  AiOutlineMenu,
  AiOutlineClose
} from 'react-icons/ai';

export default function SideBar() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  const links = [
    { to: "/dashboard", label: "الرئيسية", icon: AiOutlineHome },
    { to: "/dashboard/childrenManagement", label: "إدارة الأطفال", icon: AiOutlineUser },
    { to: "/dashboard/recommendations", label: "التوصيات المقترحة", icon: AiOutlineBulb },
    { to: "/dashboard/add-child", label: "إضافة طفل جديد", icon: AiOutlinePlus },
    { to: "/dashboard/uploadingDraw", label: "رفع رسمة جديدة", icon: AiOutlineCloudUpload },
    { to: "/dashboard/settings", label: "الإعدادات", icon: AiOutlineSetting },
  ];

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const toggleMobileSidebar = () => {
    setIsMobileOpen(!isMobileOpen);
  };

  return (
    <>
      {/* Mobile Menu Button */}
      <button
        onClick={toggleMobileSidebar}
        className="lg:hidden fixed top-4 right-4 z-50 p-2 bg-white rounded-lg shadow-md"
      >
        {isMobileOpen ? <AiOutlineClose size={24} /> : <AiOutlineMenu size={24} />}
      </button>

      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={toggleMobileSidebar}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed lg:relative top-0 right-0 h-screen bg-white shadow-xl border-l border-gray-100 z-40 transition-all duration-300 ease-in-out
        ${isCollapsed ? 'w-16' : 'w-64'}
        ${isMobileOpen ? 'translate-x-0' : 'translate-x-full lg:translate-x-0'}
      `}>

        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {!isCollapsed && (
              <div className="flex items-center gap-3">
                <img src={logo} alt="Logo" className="w-10 h-10 rounded-lg" />
                <div className="text-right">
                  <h1 className="text-lg font-bold text-gray-800">GazaKidMind</h1>
                  <p className="text-xs text-gray-500">لوحة التحكم</p>
                </div>
              </div>
            )}

            {isCollapsed && (
              <img src={logo} alt="Logo" className="w-8 h-8 rounded-lg mx-auto" />
            )}

            {/* Collapse Button - Hidden on mobile */}
            <button
              onClick={toggleSidebar}
              className="hidden lg:block p-1 hover:bg-gray-100 rounded-md transition-colors"
            >
              <AiOutlineMenu size={20} className="text-gray-600" />
            </button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="p-4 flex-1">
          <ul className="space-y-2">
            {links.map((link) => (
              <li key={link.to}>
                <NavLink
                  to={link.to}
                  end={link.to === "/dashboard"}
                  onClick={() => setIsMobileOpen(false)}
                  className={({ isActive }) =>
                    `flex items-center gap-3 p-3 rounded-lg transition-all duration-200 group ${
                      isActive
                        ? "bg-blue-50 text-blue-600 border-r-4 border-blue-600"
                        : "text-gray-700 hover:bg-gray-50 hover:text-blue-600"
                    } ${isCollapsed ? 'justify-center' : 'justify-end'}`
                  }
                >
                  {!isCollapsed && (
                    <span className="font-medium text-sm">{link.label}</span>
                  )}
                  <link.icon
                    size={20}
                    className={`${isCollapsed ? '' : 'ml-auto'} transition-colors`}
                  />

                  {/* Tooltip for collapsed state */}
                  {isCollapsed && (
                    <div className="absolute right-16 bg-gray-800 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                      {link.label}
                    </div>
                  )}
                </NavLink>
              </li>
            ))}
          </ul>
        </nav>
    </>
  )
}
