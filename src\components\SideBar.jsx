import React from 'react'
import { NavLink } from 'react-router-dom';
import logo from "../assets/logo192.png";
import home from "../assets/dashIcone/home.png" 
export default function SideBar() {
   const links = [
    { to: "/dashboard", label: "الرئيسية" , icone: home },
    { to: "/dashboard/childrenManagement", label: "إدارة الأطفال" , icone:home },
    { to: "/dashboard/", label: "التوصيات المقترحة" , icone:home },
    { to: "/dashboard/childrenManagement", label: "إضافة طفل جديد", icone:home  },
    { to: "/dashboard/uploadingDraw", label: "رفع رسمة جديدة", icone:home  },
    { to: "/dashboard/settings", label: "الإعدادت", icone:home  },
  ];
  return (
    <div className='h-screen min-w-[220px]'>
        <div className='mt-8  items-center w-full justify-end flex gap-2'>
            <span className="text-base font-bold text-gray-800">
                GazaKidMind
            </span>
            {/* صورة اللوجو */}
            <img src={logo}   alt="Logo" className="w-16 h-20" />
        </div>
      <ul className="space-y-2 flex-1">
        {links.map((link) => (
          <li key={link.to}>
            <NavLink
              to={link.to}
              end
              className={({ isActive }) =>
                `flex items-center gap-2 p-2 rounded-lg transition ${
                  isActive
                    ? "bg-white text-black"
                    : "text-gray-700 hover:bg-gray-100"
                }`
              }
            >
              <div className=' flex items-center gap-2 w-full justify-end'>
                {link.label}</div>
                <img src={link.icone} alt={link.label} />
            </NavLink>
          </li>
        ))}
      </ul>
    </div>
  )
}
